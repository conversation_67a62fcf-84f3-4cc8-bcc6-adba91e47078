#!/usr/bin/env python3
"""
简化的MCP工具测试脚本
"""

import os
import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    try:
        from dotenv import load_dotenv
        print("✅ dotenv导入成功")
        
        from autogen_ext.tools.mcp import StdioServerParams
        print("✅ MCP工具导入成功")
        
        load_dotenv(dotenv_path="MCP.env")
        print("✅ 环境变量加载成功")
        
        tavily_key = os.getenv("TAVILY_API_KEY")
        if tavily_key:
            print(f"✅ TAVILY_API_KEY已设置: {tavily_key[:10]}...")
        else:
            print("❌ TAVILY_API_KEY未设置")
            
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_system_environment():
    """测试系统环境"""
    import subprocess
    import platform
    
    print(f"操作系统: {platform.system()} {platform.release()}")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"Node.js版本: {result.stdout.strip()}")
        else:
            print("❌ Node.js不可用")
    except Exception as e:
        print(f"❌ Node.js检查失败: {e}")
    
    # 检查npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"npm版本: {result.stdout.strip()}")
        else:
            print("❌ npm不可用")
    except Exception as e:
        print(f"❌ npm检查失败: {e}")

async def test_network():
    """测试网络连接"""
    import socket
    try:
        socket.create_connection(("*******", 53), timeout=5)
        print("✅ 网络连接正常")
        return True
    except OSError as e:
        print(f"❌ 网络连接失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 简化MCP工具测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 测试基本导入
    print("\n📦 步骤1: 基本导入测试")
    print("-" * 30)
    import_ok = test_basic_imports()
    
    # 2. 测试系统环境
    print("\n🖥️ 步骤2: 系统环境检查")
    print("-" * 30)
    test_system_environment()
    
    # 3. 测试网络连接
    print("\n🌐 步骤3: 网络连接测试")
    print("-" * 30)
    network_ok = await test_network()
    
    # 4. 总结
    print("\n📊 测试结果总结")
    print("=" * 60)
    print(f"📦 基本导入: {'✅ 成功' if import_ok else '❌ 失败'}")
    print(f"🌐 网络连接: {'✅ 正常' if network_ok else '❌ 失败'}")
    
    if import_ok and network_ok:
        print("\n✅ 基础环境检查通过，可以尝试运行完整的MCP工具测试")
    else:
        print("\n❌ 基础环境存在问题，需要先解决这些问题")
    
    print("\n" + "=" * 60)
    print("🧪 简化测试完成")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
