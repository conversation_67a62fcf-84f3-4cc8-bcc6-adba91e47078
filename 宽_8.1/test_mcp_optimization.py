#!/usr/bin/env python3
"""
测试MCP工具优化效果的脚本
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from module2 import (
    initialize_mcp_tools_with_retry,
    sales_expert_analysis_without_tools,
    get_model_client,
    log_mcp_diagnostic_info,
    check_network_connectivity,
    validate_tavily_api_key
)
from autogen_ext.tools.mcp import StdioServerParams
from dotenv import load_dotenv

async def test_mcp_optimization():
    """测试MCP工具优化效果"""
    print("=" * 80)
    print("🧪 MCP工具优化测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 加载环境变量
    load_dotenv(dotenv_path="MCP.env")
    
    # 1. 测试诊断信息
    print("\n📋 步骤1: 系统诊断信息")
    print("-" * 40)
    log_mcp_diagnostic_info()
    
    # 2. 测试网络连接
    print("\n🌐 步骤2: 网络连接测试")
    print("-" * 40)
    network_ok = await check_network_connectivity()
    if network_ok:
        print("✅ 网络连接正常")
    else:
        print("❌ 网络连接失败")
        return
    
    # 3. 测试API密钥验证
    print("\n🔑 步骤3: API密钥验证")
    print("-" * 40)
    api_valid, api_message = await validate_tavily_api_key()
    print(f"API验证结果: {api_message}")
    
    # 4. 测试MCP工具初始化
    print("\n🔧 步骤4: MCP工具初始化测试")
    print("-" * 40)
    
    TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")
    if not TAVILY_API_KEY:
        print("❌ TAVILY_API_KEY环境变量未设置")
        return
    
    TAVILY_MCP_SERVER = StdioServerParams(
        command="npx",
        args=["-y", "tavily-mcp@0.2.0"],
        env={"TAVILY_API_KEY": TAVILY_API_KEY},
    )
    
    tools = await initialize_mcp_tools_with_retry(TAVILY_MCP_SERVER, max_retries=2)
    
    if tools:
        print("✅ MCP工具初始化成功")
        mcp_success = True
    else:
        print("❌ MCP工具初始化失败")
        mcp_success = False
    
    # 5. 测试无工具版本的销售专家分析
    print("\n🤖 步骤5: 无工具销售专家分析测试")
    print("-" * 40)
    
    try:
        model_client = get_model_client("meta-llama/llama-4-maverick")
        
        test_prompt = """
You are an expert in recommending industry websites.

**Task Steps:**
1. Based on the provided product information, analyze the application fields and target markets of 化学品合成设备.
2. Provide specific website recommendations for 美国 market.
3. Provide keywords for subsequent business opportunity searches.

Product Information:
- Product Name: 化学品合成设备
- Product Details: 应用于生物制药的微流控设备
- Target Country: 美国

Please provide website recommendations and search keywords.
"""
        
        result = await sales_expert_analysis_without_tools(
            model_client, test_prompt, max_retries=2
        )
        
        if result:
            print("✅ 无工具销售专家分析成功")
            print(f"📋 结果预览: {result[:200]}...")
        else:
            print("❌ 无工具销售专家分析失败")
            
    except Exception as e:
        print(f"❌ 无工具销售专家分析异常: {e}")
    
    # 6. 总结测试结果
    print("\n📊 测试结果总结")
    print("=" * 80)
    print(f"🌐 网络连接: {'✅ 正常' if network_ok else '❌ 失败'}")
    print(f"🔑 API密钥: {'✅ 有效' if api_valid else '❌ 无效'}")
    print(f"🔧 MCP工具: {'✅ 成功' if mcp_success else '❌ 失败'}")
    print(f"🤖 无工具模式: {'✅ 可用' if 'result' in locals() and result else '❌ 不可用'}")
    
    # 7. 建议
    print("\n💡 优化建议")
    print("-" * 40)
    if not network_ok:
        print("- 检查网络连接和防火墙设置")
    if not api_valid:
        print("- 检查TAVILY_API_KEY是否正确设置")
    if not mcp_success:
        print("- MCP工具初始化失败，但无工具模式可作为备用方案")
        print("- 检查Node.js环境和npm配置")
    else:
        print("- MCP工具工作正常，系统运行良好")
    
    print("\n🎯 优化效果")
    print("-" * 40)
    print("✅ 增加了详细的诊断信息")
    print("✅ 改进了错误分类和建议")
    print("✅ 实现了无工具备用方案")
    print("✅ 提高了系统的容错能力")
    
    print("\n" + "=" * 80)
    print("🧪 MCP工具优化测试完成")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(test_mcp_optimization())
