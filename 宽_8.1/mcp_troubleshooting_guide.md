# MCP工具故障排除指南

## 🎯 优化概述

本次优化主要解决了MCP工具初始化失败的问题，并实现了以下改进：

### ✅ 已实现的优化

1. **提高MCP工具初始化成功率**
   - 增加了详细的系统诊断信息
   - 改进了网络连接检查
   - 添加了API密钥验证
   - 优化了重试策略（递增等待时间）
   - 增强了错误分类和建议

2. **实现备用方案**
   - 当MCP工具初始化失败时，自动使用模型自身能力
   - 当有工具的销售专家分析失败时，回退到无工具模式
   - 确保销售专家分析不会被跳过

3. **改进错误处理**
   - 详细的错误分类和诊断建议
   - 更好的日志记录
   - 智能的错误恢复机制

## 🔧 故障排除步骤

### 1. 运行诊断测试

```bash
cd 宽_8.1
python test_mcp_optimization.py
```

这将检查：
- 系统环境（Node.js, npm, npx）
- 网络连接
- API密钥有效性
- MCP工具初始化
- 无工具备用方案

### 2. 常见问题解决

#### 问题1: "unhandled errors in a TaskGroup"
**原因**: 异步任务组中的子任务失败
**解决方案**:
- 检查Node.js是否正确安装
- 确保npm和npx可用
- 检查网络连接
- 验证TAVILY_API_KEY

#### 问题2: 网络连接失败
**原因**: 无法访问外部网络
**解决方案**:
- 检查防火墙设置
- 确认代理配置
- 测试DNS解析

#### 问题3: API密钥无效
**原因**: TAVILY_API_KEY错误或过期
**解决方案**:
- 检查MCP.env文件中的API密钥
- 确认API密钥格式正确
- 联系Tavily获取新的API密钥

#### 问题4: Node.js环境问题
**原因**: Node.js未安装或版本不兼容
**解决方案**:
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查npx版本
npx --version

# 如果未安装，请安装Node.js
# 推荐版本: Node.js 16+
```

### 3. 环境变量检查

确保MCP.env文件包含有效的API密钥：

```env
TAVILY_API_KEY=tvly-dev-your-api-key-here
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
```

### 4. 手动测试MCP工具

```bash
# 测试npx命令
npx --version

# 测试tavily-mcp包
npx -y tavily-mcp@0.2.0 --help
```

## 🚀 使用新的备用方案

### 自动备用方案

系统现在会自动处理MCP工具失败：

1. **第一次尝试**: 使用MCP工具进行销售专家分析
2. **如果失败**: 自动切换到无工具模式
3. **无工具模式**: 使用模型自身知识库进行分析

### 手动启用无工具模式

如果您希望直接使用无工具模式，可以在代码中设置：

```python
# 在analyze_business_opportunities函数中
# 将tools设置为None来强制使用无工具模式
tools = None
```

## 📊 性能对比

| 模式 | 优点 | 缺点 |
|------|------|------|
| MCP工具模式 | 实时搜索，信息最新 | 依赖网络，可能失败 |
| 无工具模式 | 稳定可靠，响应快速 | 基于训练数据，信息可能不是最新 |

## 🔍 监控和日志

### 查看详细日志

系统会在以下位置记录详细日志：
- `logs/full_log_YYYYMMDD_HHMMSS.txt`
- 控制台输出

### 关键日志信息

查找以下关键信息：
- `✅ Tavily MCP工具初始化成功`
- `❌ MCP工具初始化最终失败`
- `✅ 无工具销售专家分析成功完成`
- `⚠️ 使用模型自身能力进行销售专家分析`

## 💡 最佳实践

1. **定期检查环境**
   - 确保Node.js和npm是最新版本
   - 定期验证API密钥有效性

2. **网络优化**
   - 使用稳定的网络连接
   - 配置合适的代理设置

3. **监控系统状态**
   - 定期运行诊断测试
   - 关注日志中的警告信息

4. **备用方案准备**
   - 了解无工具模式的能力和限制
   - 在网络不稳定时考虑使用无工具模式

## 🆘 获取帮助

如果问题仍然存在：

1. 运行完整的诊断测试
2. 检查日志文件中的详细错误信息
3. 确认所有环境变量正确设置
4. 测试网络连接和API密钥

记住：即使MCP工具失败，系统现在也会使用无工具模式确保销售专家分析能够完成！
