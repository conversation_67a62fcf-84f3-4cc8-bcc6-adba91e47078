#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简洁的实时日志功能
"""
import gradio as gr
import threading
import time
from datetime import datetime

# 全局变量用于实时日志显示
_current_real_time_log = ""
_log_update_lock = threading.Lock()

def get_current_real_time_log():
    """获取当前的实时日志内容"""
    global _current_real_time_log, _log_update_lock
    try:
        with _log_update_lock:
            # 直接返回完整的实时日志内容，不添加任何头部信息
            return _current_real_time_log or "等待分析开始..."
    except Exception as e:
        return f"日志获取失败: {str(e)}"

def clear_real_time_log():
    """清空实时日志"""
    global _current_real_time_log, _log_update_lock
    try:
        with _log_update_lock:
            _current_real_time_log = ""
    except Exception:
        pass

def simulate_long_analysis():
    """模拟长时间分析过程，生成大量日志"""
    global _current_real_time_log, _log_update_lock
    
    # 清空日志
    clear_real_time_log()
    
    # 模拟大量日志输出
    for i in range(100):
        with _log_update_lock:
            log_entry = f"""
[{datetime.now().strftime('%H:%M:%S')}] 步骤 {i+1}/100: 正在处理数据...
🔍 搜索关键词: 化学品合成设备
📊 找到相关信息: {i*3 + 5} 条
💡 分析进度: {(i+1)/100*100:.1f}%
📝 详细信息: 这是第 {i+1} 步的详细分析内容，包含了大量的技术细节和市场数据
   - 技术参数分析完成
   - 市场需求评估完成  
   - 竞争对手分析完成
   - 价格趋势分析完成
✅ 第 {i+1} 步完成

"""
            _current_real_time_log += log_entry
        
        # 短暂等待，模拟真实处理时间
        time.sleep(0.5)
    
    # 添加最终结果
    with _log_update_lock:
        final_result = f"""
{'='*60}
🎉 分析完成！
⏰ 完成时间: {datetime.now().strftime('%H:%M:%S')}
📊 总共处理: 100 个步骤
📋 生成日志: {len(_current_real_time_log)} 字符
✅ 所有步骤均已成功完成
{'='*60}
"""
        _current_real_time_log += final_result
    
    return "✅ 长时间分析完成！生成了大量完整日志"

def create_test_interface():
    """创建测试界面"""
    with gr.Blocks(title="简洁实时日志测试", theme=gr.themes.Soft()) as interface:
        gr.HTML("<h1>🤖 简洁实时日志测试</h1>")
        
        with gr.Row():
            with gr.Column():
                start_btn = gr.Button("🚀 开始长时间分析", variant="primary", size="lg")
                clear_btn = gr.Button("🧹 清空日志", variant="secondary")
        
        with gr.Row():
            with gr.Column():
                # 简洁的实时日志标题，无额外说明
                gr.HTML("<h3>📺 实时日志</h3>")

                real_time_log = gr.Textbox(
                    label="实时执行日志",
                    placeholder="点击开始分析后，实时日志将在这里显示...",
                    lines=20,
                    max_lines=30,
                    show_copy_button=True,
                    interactive=False
                )
        
        with gr.Row():
            with gr.Column():
                result_output = gr.Textbox(
                    label="分析结果",
                    placeholder="分析结果将在这里显示...",
                    lines=3,
                    interactive=False
                )
        
        # 实时日志更新函数
        def update_real_time_log_display():
            """更新实时日志显示"""
            return get_current_real_time_log()
        
        def clear_log():
            """清空日志"""
            clear_real_time_log()
            return "", "日志已清空"

        # 绑定事件
        start_btn.click(
            fn=simulate_long_analysis,
            outputs=[result_output]
        )
        
        clear_btn.click(
            fn=clear_log,
            outputs=[real_time_log, result_output]
        )

        # 创建定时器用于自动更新实时日志
        timer = gr.Timer(value=2.0)  # 每2秒触发一次
        timer.tick(
            fn=update_real_time_log_display,
            outputs=[real_time_log]
        )
    
    return interface

if __name__ == "__main__":
    print("🚀 启动简洁实时日志测试...")
    interface = create_test_interface()
    interface.launch(
        server_name="127.0.0.1",
        server_port=7863,
        share=False,
        inbrowser=True
    )
